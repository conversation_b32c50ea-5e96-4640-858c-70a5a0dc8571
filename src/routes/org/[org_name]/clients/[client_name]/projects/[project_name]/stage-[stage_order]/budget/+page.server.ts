import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { error } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { buildBudgetTree, type EnhancedWbsItemTree } from '$lib/budget_utils';

export const load: PageServerLoad = async ({ params, locals, cookies, depends }) => {
	depends('project:budget-snapshot');

	await requireUser(cookies);

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject(params, cookies);
	const { stage_order } = params;

	// Fetch the project data
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('*, client!inner(name, client_id, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw redirect(
			`/org/${org_name}/clients/${client_name}`,
			{ type: 'error', message: 'Project not found' },
			cookies,
		);
	}

	// Parse stage_order from URL parameter
	const stageOrderNum = parseInt(stage_order, 10);
	if (isNaN(stageOrderNum)) {
		throw redirect(
			`/org/${org_name}/clients/${client_name}/projects/${project_name}`,
			{ type: 'error', message: 'Invalid stage order' },
			cookies,
		);
	}

	// Find the project stage by stage_order
	const { data: stageData, error: stageError } = await supabase
		.from('project_stage')
		.select('*')
		.eq('project_id', projectData.project_id)
		.eq('stage_order', stageOrderNum)
		.limit(1)
		.maybeSingle();

	if (stageError || !stageData) {
		console.error('Error fetching project stage:', stageError);
		throw redirect(
			`/org/${org_name}/clients/${client_name}/projects/${project_name}`,
			{ type: 'error', message: 'Project stage not found' },
			cookies,
		);
	}

	// Fetch budget snapshots for this stage
	const { data: budgetSnapshots, error: snapshotsError } = await supabase
		.from('budget_snapshot')
		.select('*')
		.eq('project_stage_id', stageData.project_stage_id)
		.order('freeze_date', { ascending: false });

	if (snapshotsError) {
		console.error('Error fetching budget snapshots:', snapshotsError);
		throw error(500, { message: 'Error loading budget snapshots' });
	}

	// If no snapshots exist, return empty data
	if (!budgetSnapshots || budgetSnapshots.length === 0) {
		return {
			client: projectData.client,
			project: projectData,
			stage: stageData,
			budgetSnapshots: [],
			wbsTree: [],
			hasSnapshots: false,
		};
	}

	// Get the most recent snapshot (first in the ordered list)
	const latestSnapshot = budgetSnapshots[0];

	// Fetch budget snapshot line items for the latest snapshot with WBS item details
	const { data: rawSnapshotItems, error: itemsError } = await supabase
		.from('budget_snapshot_line_item')
		.select(
			`*,
			wbs_library_item!inner(*)
		`,
		)
		.eq('budget_snapshot_id', latestSnapshot.budget_snapshot_id);

	if (itemsError) {
		console.error('Error fetching budget snapshot line items:', itemsError);
		throw error(500, { message: 'Error loading budget snapshot data' });
	}

	// Transform snapshot line items to match the structure expected by buildBudgetTree
	const transformedItems = rawSnapshotItems.map((item) => ({
		...item.wbs_library_item,
		budget_line_item_current: [
			{
				budget_line_item_id: item.budget_snapshot_line_item_id,
				project_id: projectData.project_id,
				wbs_library_item_id: item.wbs_library_item_id,
				quantity: item.quantity || 0,
				unit: item.unit,
				material_rate: item.material_rate || 0,
				labor_rate: item.labor_rate,
				productivity_per_hour: item.productivity_per_hour,
				unit_rate_manual_override: item.unit_rate_manual_override,
				unit_rate: item.unit_rate || 0,
				factor: item.factor,
				remarks: item.remarks,
				cost_certainty: item.cost_certainty,
				design_certainty: item.design_certainty,
				created_at: item.created_at,
				updated_at: item.created_at, // Use created_at since snapshots are immutable
			},
		],
	}));

	// Build the hierarchical tree structure
	const wbsTree: EnhancedWbsItemTree[] = buildBudgetTree(transformedItems);

	return {
		client: projectData.client,
		project: projectData,
		stage: stageData,
		budgetSnapshots,
		latestSnapshot,
		wbsTree,
		hasSnapshots: true,
	};
};
